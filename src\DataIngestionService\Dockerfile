# ~/trading-bot-stack/src/DataIngestionService/Dockerfile

# --- Build Stage ---
# Use the .NET SDK image to build your application
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy the .csproj file first to leverage <PERSON><PERSON>'s build cache
# This ensures that dotnet restore is only rerun if the .csproj changes
COPY ["DataIngestionService.csproj", "DataIngestionService/"]
RUN dotnet restore "DataIngestionService/DataIngestionService.csproj"

# Copy the rest of the application code
COPY . .

# Build the application
WORKDIR "/src/DataIngestionService"
RUN dotnet build "DataIngestionService.csproj" -c Release -o /app/build

# --- Publish Stage ---
# Publish the application to a self-contained folder
FROM build AS publish
WORKDIR "/src/DataIngestionService"
RUN dotnet publish "DataIngestionService.csproj" -c Release -o /app/publish /p:UseAppHost=false

# --- Final Runtime Stage ---
# Use a smaller runtime image for the final production image
FROM mcr.microsoft.com/dotnet/runtime:8.0 AS final
WORKDIR /app

# Copy only the published application output from the publish stage
COPY --from=publish /app/publish .

# Define the entry point for the container
ENTRYPOINT ["dotnet", "DataIngestionService.dll"]